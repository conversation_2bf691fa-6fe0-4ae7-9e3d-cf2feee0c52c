import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorageService {
  static const String _tajersKey = 'tajers_list';
  static const String _productsKey = 'products_list';
  static const String _invoicesKey = 'invoices_list';

  // حفظ قائمة التجار
  static Future<void> saveTajers(List<Map<String, dynamic>> tajers) async {
    final prefs = await SharedPreferences.getInstance();
    final String tajersJson = jsonEncode(tajers);
    await prefs.setString(_tajersKey, tajersJson);
  }

  // تحميل قائمة التجار
  static Future<List<Map<String, dynamic>>> loadTajers() async {
    final prefs = await SharedPreferences.getInstance();
    final String? tajersJson = prefs.getString(_tajersKey);
    if (tajersJson != null) {
      final List<dynamic> decoded = jsonDecode(tajersJson);
      return decoded.cast<Map<String, dynamic>>();
    }
    return [];
  }

  // إضافة تاجر جديد
  static Future<void> addTajer(Map<String, dynamic> tajer) async {
    final tajers = await loadTajers();
    // إنشاء معرف فريد
    final int newId = tajers.isEmpty ? 1 : (tajers.map((t) => t['id'] as int).reduce((a, b) => a > b ? a : b) + 1);
    tajer['id'] = newId;
    tajer['created_at'] = DateTime.now().toIso8601String();
    tajers.add(tajer);
    await saveTajers(tajers);
  }

  // تحديث تاجر
  static Future<void> updateTajer(Map<String, dynamic> updatedTajer) async {
    final tajers = await loadTajers();
    final index = tajers.indexWhere((t) => t['id'] == updatedTajer['id']);
    if (index != -1) {
      tajers[index] = updatedTajer;
      await saveTajers(tajers);
    }
  }

  // حذف تاجر
  static Future<void> deleteTajer(int tajerId) async {
    final tajers = await loadTajers();
    tajers.removeWhere((t) => t['id'] == tajerId);
    await saveTajers(tajers);
  }

  // حفظ قائمة المنتجات
  static Future<void> saveProducts(List<Map<String, dynamic>> products) async {
    final prefs = await SharedPreferences.getInstance();
    final String productsJson = jsonEncode(products);
    await prefs.setString(_productsKey, productsJson);
  }

  // تحميل قائمة المنتجات
  static Future<List<Map<String, dynamic>>> loadProducts() async {
    final prefs = await SharedPreferences.getInstance();
    final String? productsJson = prefs.getString(_productsKey);
    if (productsJson != null) {
      final List<dynamic> decoded = jsonDecode(productsJson);
      return decoded.cast<Map<String, dynamic>>();
    }
    return [];
  }

  // إضافة منتج جديد
  static Future<void> addProduct(Map<String, dynamic> product) async {
    final products = await loadProducts();
    // إنشاء معرف فريد
    final int newId = products.isEmpty ? 1 : (products.map((p) => p['id'] as int).reduce((a, b) => a > b ? a : b) + 1);
    product['id'] = newId;
    product['created_at'] = DateTime.now().toIso8601String();
    products.add(product);
    await saveProducts(products);
  }

  // تحديث منتج
  static Future<void> updateProduct(Map<String, dynamic> updatedProduct) async {
    final products = await loadProducts();
    final index = products.indexWhere((p) => p['id'] == updatedProduct['id']);
    if (index != -1) {
      products[index] = updatedProduct;
      await saveProducts(products);
    }
  }

  // حذف منتج
  static Future<void> deleteProduct(int productId) async {
    final products = await loadProducts();
    products.removeWhere((p) => p['id'] == productId);
    await saveProducts(products);
  }

  // حفظ قائمة الفواتير
  static Future<void> saveInvoices(List<Map<String, dynamic>> invoices) async {
    final prefs = await SharedPreferences.getInstance();
    final String invoicesJson = jsonEncode(invoices);
    await prefs.setString(_invoicesKey, invoicesJson);
  }

  // تحميل قائمة الفواتير
  static Future<List<Map<String, dynamic>>> loadInvoices() async {
    final prefs = await SharedPreferences.getInstance();
    final String? invoicesJson = prefs.getString(_invoicesKey);
    if (invoicesJson != null) {
      final List<dynamic> decoded = jsonDecode(invoicesJson);
      return decoded.cast<Map<String, dynamic>>();
    }
    return [];
  }

  // مسح جميع البيانات (للاختبار فقط)
  static Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tajersKey);
    await prefs.remove(_productsKey);
    await prefs.remove(_invoicesKey);
  }
}
