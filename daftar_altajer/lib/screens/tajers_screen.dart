import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/tajer.dart';
import '../services/tajer_service.dart';
import '../widgets/add_tajer_dialog.dart';
import '../widgets/tajer_details_screen.dart';
import '../widgets/edit_tajer_dialog.dart';

class TajersScreen extends StatefulWidget {
  const TajersScreen({super.key});

  @override
  State<TajersScreen> createState() => _TajersScreenState();
}

class _TajersScreenState extends State<TajersScreen> {
  final TajerService _tajerService = TajerService();
  final TextEditingController _searchController = TextEditingController();
  
  List<Tajer> _tajers = [];
  List<Tajer> _filteredTajers = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final tajers = await _tajerService.getAllTajers();
      final stats = await _tajerService.getTajersStatistics();
      
      setState(() {
        _tajers = tajers;
        _filteredTajers = tajers;
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    }
  }

  void _filterTajers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredTajers = _tajers;
      } else {
        _filteredTajers = _tajers.where((tajer) =>
          tajer.name.toLowerCase().contains(query.toLowerCase()) ||
          tajer.phone.contains(query)
        ).toList();
      }
    });
  }

  Future<void> _showAddTajerDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const AddTajerDialog(),
    );
    
    if (result == true) {
      _loadData();
    }
  }

  Future<void> _showEditTajerDialog(Tajer tajer) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => EditTajerDialog(tajer: tajer),
    );
    
    if (result == true) {
      _loadData();
    }
  }

  Future<void> _deleteTajer(Tajer tajer) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف التاجر "${tajer.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _tajerService.deleteTajer(tajer.id!);
        _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف التاجر بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف التاجر: $e')),
          );
        }
      }
    }
  }

  void _showTajerDetails(Tajer tajer) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TajerDetailsScreen(tajer: tajer),
      ),
    ).then((_) => _loadData());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التجار'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildStatisticsCards(),
                _buildSearchBar(),
                Expanded(child: _buildTajersList()),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddTajerDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي التجار',
              '${_statistics['totalCount'] ?? 0}',
              Icons.people,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'إجمالي الديون',
              '${(_statistics['totalDebt'] ?? 0).toStringAsFixed(1)} ج.م',
              Icons.trending_down,
              Colors.red,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'إجمالي الأرصدة',
              '${(_statistics['totalCredit'] ?? 0).toStringAsFixed(1)} ج.م',
              Icons.trending_up,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: 'البحث بالاسم أو رقم الهاتف...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: _filterTajers,
      ),
    );
  }

  Widget _buildTajersList() {
    if (_filteredTajers.isEmpty) {
      return const Center(
        child: Text('لا توجد تجار'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredTajers.length,
      itemBuilder: (context, index) {
        final tajer = _filteredTajers[index];
        return _buildTajerCard(tajer);
      },
    );
  }

  Widget _buildTajerCard(Tajer tajer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: tajer.isDebtor ? Colors.red : 
                          tajer.hasCredit ? Colors.green : Colors.grey,
          child: Text(
            tajer.name.isNotEmpty ? tajer.name[0] : 'ت',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          tajer.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('📞 ${tajer.phone}'),
            if (tajer.address?.isNotEmpty == true)
              Text('📍 ${tajer.address}'),
            Text(
              'الرصيد: ${tajer.formattedBalance}',
              style: TextStyle(
                color: tajer.isDebtor ? Colors.red : 
                       tajer.hasCredit ? Colors.green : Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'details':
                _showTajerDetails(tajer);
                break;
              case 'edit':
                _showEditTajerDialog(tajer);
                break;
              case 'delete':
                _deleteTajer(tajer);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'details',
              child: Row(
                children: [
                  Icon(Icons.info),
                  SizedBox(width: 8),
                  Text('التفاصيل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _showTajerDetails(tajer),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
