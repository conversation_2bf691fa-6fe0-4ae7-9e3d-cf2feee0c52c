import 'package:sqflite/sqflite.dart';
import '../models/tajer.dart';
import 'database_service.dart';

class TajerService {
  final DatabaseService _databaseService = DatabaseService();

  // إضافة تاجر جديد
  Future<int> insertTajer(Tajer tajer) async {
    final db = await _databaseService.database;
    return await db.insert('tajer', tajer.toMap());
  }

  // الحصول على جميع التجار
  Future<List<Tajer>> getAllTajers() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('tajer', orderBy: 'name ASC');
    return List.generate(maps.length, (i) => Tajer.fromMap(maps[i]));
  }

  // الحصول على تاجر بالمعرف
  Future<Tajer?> getTajerById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tajer',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Tajer.fromMap(maps.first);
    }
    return null;
  }

  // البحث عن التجار بالاسم
  Future<List<Tajer>> searchTajersByName(String name) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tajer',
      where: 'name LIKE ?',
      whereArgs: ['%$name%'],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Tajer.fromMap(maps[i]));
  }

  // تحديث تاجر
  Future<int> updateTajer(Tajer tajer) async {
    final db = await _databaseService.database;
    return await db.update(
      'tajer',
      tajer.toMap(),
      where: 'id = ?',
      whereArgs: [tajer.id],
    );
  }

  // حذف تاجر
  Future<int> deleteTajer(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'tajer',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // تحديث رصيد التاجر
  Future<int> updateTajerBalance(int tajerId, double newBalance) async {
    final db = await _databaseService.database;
    return await db.update(
      'tajer',
      {'balance': newBalance},
      where: 'id = ?',
      whereArgs: [tajerId],
    );
  }

  // إضافة مبلغ إلى رصيد التاجر
  Future<int> addToTajerBalance(int tajerId, double amount) async {
    final tajer = await getTajerById(tajerId);
    if (tajer != null) {
      final newBalance = tajer.balance + amount;
      return await updateTajerBalance(tajerId, newBalance);
    }
    return 0;
  }

  // خصم مبلغ من رصيد التاجر
  Future<int> subtractFromTajerBalance(int tajerId, double amount) async {
    final tajer = await getTajerById(tajerId);
    if (tajer != null) {
      final newBalance = tajer.balance - amount;
      return await updateTajerBalance(tajerId, newBalance);
    }
    return 0;
  }

  // الحصول على التجار المدينين
  Future<List<Tajer>> getDebtorTajers() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tajer',
      where: 'balance < 0',
      orderBy: 'balance ASC',
    );
    return List.generate(maps.length, (i) => Tajer.fromMap(maps[i]));
  }

  // الحصول على إجمالي الديون
  Future<double> getTotalDebt() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery(
      'SELECT SUM(balance) as total FROM tajer WHERE balance < 0',
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  // الحصول على إجمالي الأرصدة الموجبة
  Future<double> getTotalCredit() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery(
      'SELECT SUM(balance) as total FROM tajer WHERE balance > 0',
    );
    return (result.first['total'] as double?) ?? 0.0;
  }
}
