import 'package:sqflite/sqflite.dart';
import '../models/product.dart';
import 'database_service.dart';

class ProductService {
  final DatabaseService _databaseService = DatabaseService();

  // إضافة منتج جديد
  Future<int> insertProduct(Product product) async {
    final db = await _databaseService.database;
    return await db.insert('product', product.toMap());
  }

  // الحصول على جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('product', orderBy: 'name ASC');
    return List.generate(maps.length, (i) => Product.fromMap(maps[i]));
  }

  // الحصول على منتج بالمعرف
  Future<Product?> getProductById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'product',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Product.fromMap(maps.first);
    }
    return null;
  }

  // البحث عن المنتجات بالاسم
  Future<List<Product>> searchProductsByName(String name) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'product',
      where: 'name LIKE ?',
      whereArgs: ['%$name%'],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Product.fromMap(maps[i]));
  }

  // تحديث منتج
  Future<int> updateProduct(Product product) async {
    final db = await _databaseService.database;
    return await db.update(
      'product',
      product.toMap(),
      where: 'id = ?',
      whereArgs: [product.id],
    );
  }

  // حذف منتج
  Future<int> deleteProduct(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'product',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // تحديث كمية المنتج
  Future<int> updateProductQuantity(int productId, int newQuantity) async {
    final db = await _databaseService.database;
    return await db.update(
      'product',
      {'quantity_in_stock': newQuantity},
      where: 'id = ?',
      whereArgs: [productId],
    );
  }

  // إضافة كمية إلى المخزون
  Future<int> addToProductStock(int productId, int quantity) async {
    final product = await getProductById(productId);
    if (product != null) {
      final newQuantity = product.quantityInStock + quantity;
      return await updateProductQuantity(productId, newQuantity);
    }
    return 0;
  }

  // خصم كمية من المخزون
  Future<int> subtractFromProductStock(int productId, int quantity) async {
    final product = await getProductById(productId);
    if (product != null) {
      final newQuantity = product.quantityInStock - quantity;
      return await updateProductQuantity(productId, newQuantity);
    }
    return 0;
  }

  // الحصول على المنتجات منخفضة المخزون
  Future<List<Product>> getLowStockProducts({int threshold = 5}) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'product',
      where: 'quantity_in_stock <= ?',
      whereArgs: [threshold],
      orderBy: 'quantity_in_stock ASC',
    );
    return List.generate(maps.length, (i) => Product.fromMap(maps[i]));
  }

  // الحصول على المنتجات النافدة
  Future<List<Product>> getOutOfStockProducts() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'product',
      where: 'quantity_in_stock = 0',
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => Product.fromMap(maps[i]));
  }

  // الحصول على إجمالي قيمة المخزون
  Future<double> getTotalStockValue() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery(
      'SELECT SUM(unit_price * quantity_in_stock) as total FROM product',
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  // الحصول على عدد المنتجات
  Future<int> getProductsCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM product');
    return (result.first['count'] as int?) ?? 0;
  }
}
