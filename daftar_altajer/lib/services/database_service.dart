import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'daftar_altajer.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // جدول التجار
    await db.execute('''
      CREATE TABLE tajer (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        address TEXT,
        balance REAL NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        notes TEXT
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE product (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        unit_price REAL NOT NULL,
        quantity_in_stock INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // جدول الطلبات
    await db.execute('''
      CREATE TABLE orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tajer_id INTEGER NOT NULL,
        date TEXT NOT NULL,
        total_price REAL NOT NULL,
        FOREIGN KEY (tajer_id) REFERENCES tajer (id)
      )
    ''');

    // جدول تفاصيل الطلبات
    await db.execute('''
      CREATE TABLE order_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (product_id) REFERENCES product (id)
      )
    ''');

    // جدول الدفعات
    await db.execute('''
      CREATE TABLE payment (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tajer_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        method TEXT NOT NULL,
        date TEXT NOT NULL,
        note TEXT,
        FOREIGN KEY (tajer_id) REFERENCES tajer (id)
      )
    ''');

    // جدول الإعدادات
    await db.execute('''
      CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL
      )
    ''');

    // إدراج بيانات تجريبية
    await _insertSampleData(db);
  }

  Future<void> _insertSampleData(Database db) async {
    // إضافة تجار تجريبيين
    await db.insert('tajer', {
      'name': 'أحمد محمد',
      'phone': '01234567890',
      'address': 'القاهرة، مصر الجديدة',
      'balance': -1500.0,
      'created_at': DateTime.now().toIso8601String(),
      'notes': 'تاجر منتظم'
    });

    await db.insert('tajer', {
      'name': 'محمد علي',
      'phone': '01098765432',
      'address': 'الجيزة، الدقي',
      'balance': 2500.0,
      'created_at': DateTime.now().subtract(const Duration(days: 10)).toIso8601String(),
      'notes': 'عميل مميز'
    });

    await db.insert('tajer', {
      'name': 'فاطمة أحمد',
      'phone': '01555666777',
      'address': 'الإسكندرية، سيدي جابر',
      'balance': 0.0,
      'created_at': DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
      'notes': ''
    });
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
