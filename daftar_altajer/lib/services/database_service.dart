import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/tajer.dart';
import '../models/product.dart';
import '../models/order.dart';
import '../models/payment.dart';
import '../models/settings.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'daftar_altajer.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // جدول التجار
    await db.execute('''
      CREATE TABLE tajer (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        balance REAL NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE product (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        unit_price REAL NOT NULL,
        quantity_in_stock INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // جدول الطلبات
    await db.execute('''
      CREATE TABLE orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tajer_id INTEGER NOT NULL,
        date TEXT NOT NULL,
        total_price REAL NOT NULL,
        FOREIGN KEY (tajer_id) REFERENCES tajer (id)
      )
    ''');

    // جدول تفاصيل الطلبات
    await db.execute('''
      CREATE TABLE order_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (product_id) REFERENCES product (id)
      )
    ''');

    // جدول الدفعات
    await db.execute('''
      CREATE TABLE payment (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tajer_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        method TEXT NOT NULL,
        date TEXT NOT NULL,
        note TEXT,
        FOREIGN KEY (tajer_id) REFERENCES tajer (id)
      )
    ''');

    // جدول الإعدادات
    await db.execute('''
      CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL
      )
    ''');

    // إدراج الإعدادات الافتراضية
    await _insertDefaultSettings(db);
  }

  Future<void> _insertDefaultSettings(Database db) async {
    final defaultSettings = [
      AppSettings(key: SettingsKeys.currency, value: DefaultSettings.currency),
      AppSettings(key: SettingsKeys.companyName, value: DefaultSettings.companyName),
      AppSettings(key: SettingsKeys.companyPhone, value: DefaultSettings.companyPhone),
      AppSettings(key: SettingsKeys.companyAddress, value: DefaultSettings.companyAddress),
      AppSettings(key: SettingsKeys.autoBackup, value: DefaultSettings.autoBackup),
      AppSettings(key: SettingsKeys.googleDriveEnabled, value: DefaultSettings.googleDriveEnabled),
      AppSettings(key: SettingsKeys.lowStockAlert, value: DefaultSettings.lowStockAlert),
      AppSettings(key: SettingsKeys.lowStockThreshold, value: DefaultSettings.lowStockThreshold),
    ];

    for (var setting in defaultSettings) {
      await db.insert('settings', setting.toMap());
    }
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
