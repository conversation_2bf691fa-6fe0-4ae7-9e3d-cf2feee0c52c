class Product {
  final int? id;
  final String name;
  final double unitPrice;
  final int quantityInStock;

  Product({
    this.id,
    required this.name,
    required this.unitPrice,
    required this.quantityInStock,
  });

  // تحويل من Map إلى Object
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      unitPrice: map['unit_price'].toDouble(),
      quantityInStock: map['quantity_in_stock'],
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'unit_price': unitPrice,
      'quantity_in_stock': quantityInStock,
    };
  }

  // نسخ مع تعديل
  Product copyWith({
    int? id,
    String? name,
    double? unitPrice,
    int? quantityInStock,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      unitPrice: unitPrice ?? this.unitPrice,
      quantityInStock: quantityInStock ?? this.quantityInStock,
    );
  }

  // التحقق من نقص المخزون
  bool get isLowStock => quantityInStock <= 5;

  @override
  String toString() {
    return 'Product{id: $id, name: $name, unitPrice: $unitPrice, quantityInStock: $quantityInStock}';
  }
}
