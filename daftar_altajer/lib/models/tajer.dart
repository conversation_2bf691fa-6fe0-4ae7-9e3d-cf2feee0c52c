class Tajer {
  final int? id;
  final String name;
  final String phone;
  final String? address;
  final double balance;
  final DateTime createdAt;
  final String? notes;

  Tajer({
    this.id,
    required this.name,
    required this.phone,
    this.address,
    required this.balance,
    required this.createdAt,
    this.notes,
  });

  // تحويل من Map إلى Object
  factory Tajer.fromMap(Map<String, dynamic> map) {
    return Tajer(
      id: map['id'],
      name: map['name'],
      phone: map['phone'],
      address: map['address'],
      balance: map['balance']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at']),
      notes: map['notes'],
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'address': address,
      'balance': balance,
      'created_at': createdAt.toIso8601String(),
      'notes': notes,
    };
  }

  // نسخ مع تعديل
  Tajer copyWith({
    int? id,
    String? name,
    String? phone,
    String? address,
    double? balance,
    DateTime? createdAt,
    String? notes,
  }) {
    return Tajer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
      notes: notes ?? this.notes,
    );
  }

  // التحقق من حالة الرصيد
  bool get isDebtor => balance < 0;
  bool get hasCredit => balance > 0;
  bool get isBalanced => balance == 0;

  // تنسيق الرصيد
  String get formattedBalance {
    if (balance >= 0) {
      return '+${balance.toStringAsFixed(2)} ج.م';
    } else {
      return '${balance.toStringAsFixed(2)} ج.م';
    }
  }

  // حالة الرصيد كنص
  String get balanceStatus {
    if (isDebtor) return 'مدين';
    if (hasCredit) return 'دائن';
    return 'متوازن';
  }

  @override
  String toString() {
    return 'Tajer{id: $id, name: $name, phone: $phone, balance: $balance}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Tajer && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
