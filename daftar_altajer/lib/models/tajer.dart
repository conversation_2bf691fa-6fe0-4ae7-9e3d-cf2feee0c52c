class Tajer {
  final int? id;
  final String name;
  final String phone;
  final double balance;
  final DateTime createdAt;

  Tajer({
    this.id,
    required this.name,
    required this.phone,
    required this.balance,
    required this.createdAt,
  });

  // تحويل من Map إلى Object
  factory Tajer.fromMap(Map<String, dynamic> map) {
    return Tajer(
      id: map['id'],
      name: map['name'],
      phone: map['phone'],
      balance: map['balance'].toDouble(),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'balance': balance,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // نسخ مع تعديل
  Tajer copyWith({
    int? id,
    String? name,
    String? phone,
    double? balance,
    DateTime? createdAt,
  }) {
    return Tajer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Tajer{id: $id, name: $name, phone: $phone, balance: $balance, createdAt: $createdAt}';
  }
}
