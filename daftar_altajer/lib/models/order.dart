class Order {
  final int? id;
  final int tajerId;
  final DateTime date;
  final double totalPrice;

  Order({
    this.id,
    required this.tajerId,
    required this.date,
    required this.totalPrice,
  });

  // تحويل من Map إلى Object
  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      tajerId: map['tajer_id'],
      date: DateTime.parse(map['date']),
      totalPrice: map['total_price'].toDouble(),
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tajer_id': tajerId,
      'date': date.toIso8601String(),
      'total_price': totalPrice,
    };
  }

  // نسخ مع تعديل
  Order copyWith({
    int? id,
    int? tajerId,
    DateTime? date,
    double? totalPrice,
  }) {
    return Order(
      id: id ?? this.id,
      tajerId: tajerId ?? this.tajerId,
      date: date ?? this.date,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  @override
  String toString() {
    return 'Order{id: $id, tajerId: $tajerId, date: $date, totalPrice: $totalPrice}';
  }
}

class OrderDetail {
  final int? id;
  final int orderId;
  final int productId;
  final int quantity;
  final double unitPrice;

  OrderDetail({
    this.id,
    required this.orderId,
    required this.productId,
    required this.quantity,
    required this.unitPrice,
  });

  // تحويل من Map إلى Object
  factory OrderDetail.fromMap(Map<String, dynamic> map) {
    return OrderDetail(
      id: map['id'],
      orderId: map['order_id'],
      productId: map['product_id'],
      quantity: map['quantity'],
      unitPrice: map['unit_price'].toDouble(),
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'order_id': orderId,
      'product_id': productId,
      'quantity': quantity,
      'unit_price': unitPrice,
    };
  }

  // حساب المجموع الفرعي
  double get subtotal => quantity * unitPrice;

  // نسخ مع تعديل
  OrderDetail copyWith({
    int? id,
    int? orderId,
    int? productId,
    int? quantity,
    double? unitPrice,
  }) {
    return OrderDetail(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
    );
  }

  @override
  String toString() {
    return 'OrderDetail{id: $id, orderId: $orderId, productId: $productId, quantity: $quantity, unitPrice: $unitPrice}';
  }
}
