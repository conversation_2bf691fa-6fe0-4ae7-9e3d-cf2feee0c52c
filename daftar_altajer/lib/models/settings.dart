class AppSettings {
  final int? id;
  final String key;
  final String value;

  AppSettings({
    this.id,
    required this.key,
    required this.value,
  });

  // تحويل من Map إلى Object
  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      id: map['id'],
      key: map['key'],
      value: map['value'],
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'key': key,
      'value': value,
    };
  }

  // نسخ مع تعديل
  AppSettings copyWith({
    int? id,
    String? key,
    String? value,
  }) {
    return AppSettings(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
    );
  }

  @override
  String toString() {
    return 'AppSettings{id: $id, key: $key, value: $value}';
  }
}

// مفاتيح الإعدادات المحددة مسبقاً
class SettingsKeys {
  static const String currency = 'currency';
  static const String companyName = 'company_name';
  static const String companyPhone = 'company_phone';
  static const String companyAddress = 'company_address';
  static const String lastBackupDate = 'last_backup_date';
  static const String autoBackup = 'auto_backup';
  static const String googleDriveEnabled = 'google_drive_enabled';
  static const String lowStockAlert = 'low_stock_alert';
  static const String lowStockThreshold = 'low_stock_threshold';
}

// القيم الافتراضية للإعدادات
class DefaultSettings {
  static const String currency = 'ج.م';
  static const String companyName = 'مصنع المنتجات';
  static const String companyPhone = '';
  static const String companyAddress = '';
  static const String autoBackup = 'true';
  static const String googleDriveEnabled = 'false';
  static const String lowStockAlert = 'true';
  static const String lowStockThreshold = '5';
}
