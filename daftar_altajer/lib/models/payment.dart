enum PaymentMethod {
  cash,
  transfer,
  check,
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.transfer:
        return 'تحويل';
      case PaymentMethod.check:
        return 'شيك';
    }
  }

  String get value {
    switch (this) {
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.transfer:
        return 'transfer';
      case PaymentMethod.check:
        return 'check';
    }
  }

  static PaymentMethod fromString(String value) {
    switch (value) {
      case 'cash':
        return PaymentMethod.cash;
      case 'transfer':
        return PaymentMethod.transfer;
      case 'check':
        return PaymentMethod.check;
      default:
        return PaymentMethod.cash;
    }
  }
}

class Payment {
  final int? id;
  final int tajerId;
  final double amount;
  final PaymentMethod method;
  final DateTime date;
  final String? note;

  Payment({
    this.id,
    required this.tajerId,
    required this.amount,
    required this.method,
    required this.date,
    this.note,
  });

  // تحويل من Map إلى Object
  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id'],
      tajerId: map['tajer_id'],
      amount: map['amount'].toDouble(),
      method: PaymentMethodExtension.fromString(map['method']),
      date: DateTime.parse(map['date']),
      note: map['note'],
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tajer_id': tajerId,
      'amount': amount,
      'method': method.value,
      'date': date.toIso8601String(),
      'note': note,
    };
  }

  // نسخ مع تعديل
  Payment copyWith({
    int? id,
    int? tajerId,
    double? amount,
    PaymentMethod? method,
    DateTime? date,
    String? note,
  }) {
    return Payment(
      id: id ?? this.id,
      tajerId: tajerId ?? this.tajerId,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      date: date ?? this.date,
      note: note ?? this.note,
    );
  }

  @override
  String toString() {
    return 'Payment{id: $id, tajerId: $tajerId, amount: $amount, method: $method, date: $date, note: $note}';
  }
}
