import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/tajer.dart';
import '../services/tajer_service.dart';
import 'edit_tajer_dialog.dart';

class TajerDetailsScreen extends StatefulWidget {
  final Tajer tajer;

  const TajerDetailsScreen({super.key, required this.tajer});

  @override
  State<TajerDetailsScreen> createState() => _TajerDetailsScreenState();
}

class _TajerDetailsScreenState extends State<TajerDetailsScreen> {
  final TajerService _tajerService = TajerService();
  late Tajer _currentTajer;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentTajer = widget.tajer;
    _refreshTajerData();
  }

  Future<void> _refreshTajerData() async {
    setState(() => _isLoading = true);
    try {
      final updatedTajer = await _tajerService.getTajerById(_currentTajer.id!);
      if (updatedTajer != null) {
        setState(() {
          _currentTajer = updatedTajer;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحديث البيانات: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _showEditDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => EditTajerDialog(tajer: _currentTajer),
    );
    
    if (result == true) {
      _refreshTajerData();
    }
  }

  Future<void> _showBalanceUpdateDialog() async {
    final TextEditingController amountController = TextEditingController();
    final TextEditingController noteController = TextEditingController();
    bool isPayment = true; // true للدفع، false للخصم

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('تحديث الرصيد'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('الرصيد الحالي: ${_currentTajer.formattedBalance}'),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('دفعة'),
                      value: true,
                      groupValue: isPayment,
                      onChanged: (value) {
                        setDialogState(() => isPayment = value!);
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('خصم'),
                      value: false,
                      groupValue: isPayment,
                      onChanged: (value) {
                        setDialogState(() => isPayment = value!);
                      },
                    ),
                  ),
                ],
              ),
              TextField(
                controller: amountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ (ج.م)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: noteController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظة',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final amount = double.tryParse(amountController.text);
                if (amount != null && amount > 0) {
                  try {
                    if (isPayment) {
                      await _tajerService.addToTajerBalance(_currentTajer.id!, amount);
                    } else {
                      await _tajerService.subtractFromTajerBalance(_currentTajer.id!, amount);
                    }
                    Navigator.of(context).pop(true);
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ في تحديث الرصيد: $e')),
                    );
                  }
                }
              },
              child: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );

    if (result == true) {
      _refreshTajerData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث الرصيد بنجاح')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_currentTajer.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _showEditDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshTajerData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTajerInfoCard(),
                  const SizedBox(height: 16),
                  _buildBalanceCard(),
                  const SizedBox(height: 16),
                  _buildQuickActionsCard(),
                  const SizedBox(height: 16),
                  _buildTransactionsCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildTajerInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات التاجر',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('الاسم', _currentTajer.name, Icons.person),
            _buildInfoRow('الهاتف', _currentTajer.phone, Icons.phone),
            if (_currentTajer.address?.isNotEmpty == true)
              _buildInfoRow('العنوان', _currentTajer.address!, Icons.location_on),
            _buildInfoRow(
              'تاريخ التسجيل',
              DateFormat('dd/MM/yyyy').format(_currentTajer.createdAt),
              Icons.calendar_today,
            ),
            if (_currentTajer.notes?.isNotEmpty == true)
              _buildInfoRow('ملاحظات', _currentTajer.notes!, Icons.note),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الرصيد الحالي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _currentTajer.isDebtor ? Colors.red[50] : 
                             _currentTajer.hasCredit ? Colors.green[50] : Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _currentTajer.isDebtor ? Colors.red : 
                               _currentTajer.hasCredit ? Colors.green : Colors.grey,
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          _currentTajer.formattedBalance,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: _currentTajer.isDebtor ? Colors.red : 
                                   _currentTajer.hasCredit ? Colors.green : Colors.grey,
                          ),
                        ),
                        Text(
                          _currentTajer.balanceStatus,
                          style: TextStyle(
                            fontSize: 16,
                            color: _currentTajer.isDebtor ? Colors.red : 
                                   _currentTajer.hasCredit ? Colors.green : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _showBalanceUpdateDialog,
                    icon: const Icon(Icons.account_balance_wallet),
                    label: const Text('تحديث الرصيد'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // TODO: إضافة طلب جديد
                    },
                    icon: const Icon(Icons.add_shopping_cart),
                    label: const Text('طلب جديد'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعاملات الأخيرة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Center(
              child: Text(
                'لا توجد معاملات حتى الآن',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            // TODO: إضافة قائمة المعاملات الفعلية
          ],
        ),
      ),
    );
  }
}
