{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "google_sign_in_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "google_sign_in_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "google_sign_in_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "google_sign_in_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/", "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "printing", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}], "date_created": "2025-07-12 12:13:12.707384", "version": "3.32.6", "swift_package_manager_enabled": {"ios": false, "macos": false}}